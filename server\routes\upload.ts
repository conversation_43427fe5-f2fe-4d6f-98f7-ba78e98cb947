import { Router, Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { randomBytes } from 'crypto';
import { isAdmin } from '../middleware/auth';

const uploadRouter = Router();

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(process.cwd(), 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer storage
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    const randomId = randomBytes(8).toString('hex');
    const fileExt = path.extname(file.originalname);
    cb(null, `${Date.now()}-${randomId}${fileExt}`);
  }
});

// File filter to only allow image files
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'));
  }
};

// Configure multer upload
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB max file size
  },
  fileFilter: fileFilter
});

// Image upload endpoint
uploadRouter.post('/image', upload.single('image'), (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No image file provided' });
    }

    // Get the server URL
    const protocol = req.protocol;
    const host = req.get('host');
    const baseUrl = `${protocol}://${host}`;

    // Create the image URL
    const imageUrl = `${baseUrl}/uploads/${req.file.filename}`;

    // Return the image URL
    res.status(200).json({
      url: imageUrl,
      filename: req.file.filename
    });
  } catch (error) {
    console.error('Error uploading image:', error);
    res.status(500).json({
      message: 'Failed to upload image',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Base64 image upload endpoint (fallback for browsers that don't support FormData)
uploadRouter.post('/image-base64', (req: Request, res: Response) => {
  try {
    const { imageData } = req.body;

    if (!imageData || !imageData.startsWith('data:image/')) {
      return res.status(400).json({ message: 'Valid image data is required' });
    }

    // Extract the image type and data
    const matches = imageData.match(/^data:image\/([a-zA-Z]+);base64,(.+)$/);

    if (!matches || matches.length !== 3) {
      return res.status(400).json({ message: 'Invalid image data format' });
    }

    const imageType = matches[1];
    const base64Data = matches[2];
    const buffer = Buffer.from(base64Data, 'base64');

    // Generate a random filename
    const randomId = randomBytes(8).toString('hex');
    const filename = `${Date.now()}-${randomId}.${imageType}`;
    const filePath = path.join(uploadsDir, filename);

    // Save the file
    fs.writeFileSync(filePath, buffer);

    // Get the server URL
    const protocol = req.protocol;
    const host = req.get('host');
    const baseUrl = `${protocol}://${host}`;

    // Create the image URL
    const imageUrl = `${baseUrl}/uploads/${filename}`;

    // Return the image URL
    res.status(200).json({
      url: imageUrl,
      filename: filename
    });
  } catch (error) {
    console.error('Error processing base64 image upload:', error);
    res.status(500).json({
      message: 'Failed to process image upload',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default uploadRouter;
